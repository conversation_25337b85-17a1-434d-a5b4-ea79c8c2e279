{"kind": "singleType", "collectionName": "commission_configs", "info": {"singularName": "commission-config", "pluralName": "commission-configs", "displayName": "Commission Config", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"pro_tier_percentage": {"type": "decimal"}, "premium_tier_percentage": {"type": "decimal"}, "pro_tier_names": {"type": "text", "description": "Comma-separated tier names that qualify as pro tier (e.g. pro-month,pro-year)"}, "premium_tier_names": {"type": "text", "description": "Comma-separated tier names that qualify as premium tier (e.g. premium-month,premium-year)"}, "default_commission_percentage": {"type": "decimal", "default": 0}, "payout_cycle": {"type": "enumeration", "enum": ["weekly", "biweekly", "monthly"]}, "minimum_payout": {"type": "decimal", "default": 50}, "processing_fee": {"type": "decimal"}, "reverse_rate": {"type": "integer"}, "cookie_duration": {"type": "integer"}}}