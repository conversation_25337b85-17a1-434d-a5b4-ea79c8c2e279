/**
 * transaction controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::transaction.transaction', ({ strapi }) => ({
  /**
   * Get user's transaction history
   */
  async getUserTransactions(ctx) {
    try {
      // Check if user is authenticated
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('You must be logged in to view your transactions');
      }

      const transactionService = strapi.service('api::transaction.transaction');
      const transactions = await transactionService.getUserTransactions(user.id);

      return { data: transactions };
    } catch (error) {
      ctx.throw(500, error.message || 'An error occurred while fetching your transactions');
    }
  },

  /**
   * Process payment webhook
   * This would typically be called by a payment processor webhook
   */
  async processPayment(ctx) {
    try {
      const { transactionId, paymentResult } = ctx.request.body;

      if (!transactionId) {
        return ctx.badRequest('Transaction ID is required');
      }

      if (!paymentResult) {
        return ctx.badRequest('Payment result is required');
      }

      const transactionService = strapi.service('api::transaction.transaction');
      const updatedTransaction = await transactionService.processPayment(
        transactionId,
        paymentResult
      );

      return {
        data: updatedTransaction,
        message: `Payment ${paymentResult.success ? 'successful' : 'failed'}`,
      };
    } catch (error) {
      ctx.throw(500, error.message || 'An error occurred while processing the payment');
    }
  },

  /**
   * Generate invoice for a transaction
   */
  async generateInvoice(ctx) {
    try {
      // Check if user is authenticated
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('You must be logged in to generate an invoice');
      }

      const { id } = ctx.params;

      if (!id) {
        return ctx.badRequest('Transaction ID is required');
      }

      const transactionService = strapi.service('api::transaction.transaction');
      const invoice = await transactionService.generateInvoice(id);

      return { data: invoice };
    } catch (error) {
      ctx.throw(500, error.message || 'An error occurred while generating the invoice');
    }
  },
}));
