// Lifecycle hooks are now handled directly in the controller
export default {
  // After finding referrers, enrich with aggregated counts
  async afterFindMany(event) {
    const { result } = event;

    if (!result || !result.length) {
      return;
    }

    strapi.log.info('Referrer lifecycle: afterFindMany triggered', {
      referrersCount: result.length,
    });

    // Get commission service
    const commissionService = strapi.service('api::referral-commission.referral-commission') as any;

    // Add aggregated counts for each referrer
    for (let i = 0; i < result.length; i++) {
      const referrer = result[i];

      strapi.log.debug(`Referrer lifecycle: Processing referrer ID ${referrer.id}`);

      // Get all referrer links for this referrer
      const referrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          filters: {
            referrer: {
              id: referrer.id,
            },
          },
          fields: ['visitors', 'leads', 'conversions'],
        }
      );

      // Count all referrals (customers) for this referrer
      const totalCustomers = await strapi.entityService.count('api::referral.referral', {
        filters: {
          referrer: {
            id: referrer.id,
          },
          referral_status: 'conversion',
        },
      });

      // Get commission stats using the service
      const commissionStats = await commissionService.getCommissionStats(referrer.id, false);

      strapi.log.debug(
        `Referrer lifecycle: Found ${referrerLinks.length} links, ${totalCustomers} customers for referrer ${referrer.id}`
      );

      // Calculate total counts
      const totalClicks = referrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
      const totalLeads = referrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);
      const totalConversions = referrerLinks.reduce(
        (sum, link) => sum + (link.conversions || 0),
        0
      );

      strapi.log.debug(
        `Referrer lifecycle: Referrer ${referrer.id} totals - Clicks: ${totalClicks}, Leads: ${totalLeads}, Conversions: ${totalConversions}, Customers: ${totalCustomers}, Earnings: ${commissionStats.totalEarnings}, Revenue: ${commissionStats.totalRevenue}`
      );

      // Add the counts to the referrer object
      result[i] = {
        ...referrer,
        // totalClicks,
        // totalLeads,
        // totalConversions,
        totalCustomers,
        totalEarnings: commissionStats.totalEarnings,
        totalRevenue: commissionStats.totalRevenue,
        commissionStats,
      };
    }

    strapi.log.info('Referrer lifecycle: Successfully enriched all referrers with counts');
  },

  // After finding a single referrer, enrich with aggregated counts
  async afterFindOne(event) {
    const { result } = event;

    if (!result) {
      return;
    }

    strapi.log.info('Referrer lifecycle: afterFindOne triggered', {
      referrerId: result.id,
    });

    // Get commission service
    const commissionService = strapi.service('api::referral-commission.referral-commission') as any;

    // Get all referrer links for this referrer
    const referrerLinks = await strapi.entityService.findMany('api::referrer-link.referrer-link', {
      filters: {
        referrer: {
          id: result.id,
        },
      },
      fields: ['visitors', 'leads', 'conversions'],
    });

    // Count all referrals (customers) for this referrer
    const totalCustomers = await strapi.entityService.count('api::referral.referral', {
      filters: {
        referrer: {
          id: result.id,
        },
      },
    });

    // Get commission stats using the service
    const commissionStats = await commissionService.getCommissionStats(result.id, false);

    strapi.log.debug(
      `Referrer lifecycle: Found ${referrerLinks.length} links, ${totalCustomers} customers for referrer ${result.id}`
    );

    // Calculate total counts
    const totalClicks = referrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
    const totalLeads = referrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);
    const totalConversions = referrerLinks.reduce((sum, link) => sum + (link.conversions || 0), 0);

    strapi.log.debug(
      `Referrer lifecycle: Referrer ${result.id} totals - Clicks: ${totalClicks}, Leads: ${totalLeads}, Conversions: ${totalConversions}, Customers: ${totalCustomers}, Earnings: ${commissionStats.totalEarnings}, Revenue: ${commissionStats.totalRevenue}`
    );

    // Add the counts to the referrer object
    event.result = {
      ...result,
      totalClicks,
      totalLeads,
      totalConversions,
      totalCustomers,
      totalEarnings: commissionStats.totalEarnings,
      totalRevenue: commissionStats.totalRevenue,
      commissionStats,
    };

    strapi.log.info('Referrer lifecycle: Successfully enriched referrer with counts');
  },
};
