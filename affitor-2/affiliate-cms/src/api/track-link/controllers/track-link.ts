/**
 * track-link controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::track-link.track-link', ({ strapi }) => ({
  async getOverview(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.badRequest('Authentication required');
    }

    const referrer = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: {
        user: {
          id: user.id,
        },
      },
      limit: 1,
    });
    if (!referrer || referrer.length === 0) {
      return ctx.badRequest('No referrer found for the authenticated user');
    }

    // Calculate date ranges
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const baseFilters = {
      referrer_link: {
        referrer: {
          id: referrer[0].id,
        },
      },
    };

    // Get today's counts for each type
    const visitorsCountToday = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'visitors',
        createdAt: {
          $gte: today.toISOString(),
          $lt: tomorrow.toISOString(),
        },
      },
    });

    const conversionsCountToday = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'conversions',
        createdAt: {
          $gte: today.toISOString(),
          $lt: tomorrow.toISOString(),
        },
      },
    });

    const leadsCountToday = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'leads',
        createdAt: {
          $gte: today.toISOString(),
          $lt: tomorrow.toISOString(),
        },
      },
    });

    // Get yesterday's counts for each type
    const visitorsCountYesterday = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'visitors',
        createdAt: {
          $gte: yesterday.toISOString(),
          $lt: today.toISOString(),
        },
      },
    });

    const conversionsCountYesterday = await strapi.entityService.count(
      'api::track-link.track-link',
      {
        filters: {
          ...baseFilters,
          type: 'conversions',
          createdAt: {
            $gte: yesterday.toISOString(),
            $lt: today.toISOString(),
          },
        },
      }
    );

    const leadsCountYesterday = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'leads',
        createdAt: {
          $gte: yesterday.toISOString(),
          $lt: today.toISOString(),
        },
      },
    });

    // Get total counts for each type (all time)
    const visitorsCountTotal = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'visitors',
      },
    });

    const conversionsCountTotal = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'conversions',
      },
    });

    const leadsCountTotal = await strapi.entityService.count('api::track-link.track-link', {
      filters: {
        ...baseFilters,
        type: 'leads',
      },
    });

    // Calculate trends
    const calculateTrend = (today: number, yesterday: number) => {
      const difference = today - yesterday;
      const percentage =
        yesterday > 0 ? ((difference / yesterday) * 100).toFixed(1) : today > 0 ? '100.0' : '0.0';
      return {
        difference,
        percentage: parseFloat(percentage),
        trend: difference > 0 ? 'increase' : difference < 0 ? 'decrease' : 'stable',
      };
    };

    return {
      visitors: {
        today: visitorsCountToday,
        yesterday: visitorsCountYesterday,
        total: visitorsCountTotal,
        ...calculateTrend(visitorsCountToday, visitorsCountYesterday),
      },
      conversions: {
        today: conversionsCountToday,
        yesterday: conversionsCountYesterday,
        total: conversionsCountTotal,
        ...calculateTrend(conversionsCountToday, conversionsCountYesterday),
      },
      leads: {
        today: leadsCountToday,
        yesterday: leadsCountYesterday,
        total: leadsCountTotal,
        ...calculateTrend(leadsCountToday, leadsCountYesterday),
      },
      total: {
        today: visitorsCountToday + conversionsCountToday + leadsCountToday,
        yesterday: visitorsCountYesterday + conversionsCountYesterday + leadsCountYesterday,
        total: visitorsCountTotal + conversionsCountTotal + leadsCountTotal,
        ...calculateTrend(
          visitorsCountToday + conversionsCountToday + leadsCountToday,
          visitorsCountYesterday + conversionsCountYesterday + leadsCountYesterday
        ),
      },
    };
  },

  async getPerformanceOverview(ctx) {
    const user = ctx.state.user;
    const { period = 'weekly' } = ctx.query;

    if (!user) {
      return ctx.badRequest('Authentication required');
    }

    if (!['weekly', 'monthly', 'yearly'].includes(period as string)) {
      return ctx.badRequest('Invalid period. Must be weekly, monthly, or yearly');
    }

    const referrer = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: {
        user: {
          id: user.id,
        },
      },
      limit: 1,
    });

    if (!referrer || referrer.length === 0) {
      return ctx.badRequest('No referrer found for the authenticated user');
    }

    // Calculate date ranges and periods
    const now = new Date();
    const periods = [];
    const timeLabels = [];

    if (period === 'weekly') {
      // Last 4 weeks including current week
      for (let i = 3; i >= 0; i--) {
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - i * 7 - now.getDay());
        weekStart.setHours(0, 0, 0, 0);

        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        periods.push({ start: weekStart, end: weekEnd });
        // Use weekEnd for the time label to show the end of the week period
        timeLabels.push(weekEnd.toLocaleDateString('en-US', { month: 'short', day: '2-digit' }));
      }
    } else if (period === 'monthly') {
      // Last 4 months
      for (let i = 3; i >= 0; i--) {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);

        periods.push({ start: monthStart, end: monthEnd });
        timeLabels.push(
          monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        );
      }
    } else if (period === 'yearly') {
      // Last 4 years
      for (let i = 3; i >= 0; i--) {
        const yearStart = new Date(now.getFullYear() - i, 0, 1);
        const yearEnd = new Date(now.getFullYear() - i, 11, 31);
        yearEnd.setHours(23, 59, 59, 999);

        periods.push({ start: yearStart, end: yearEnd });
        timeLabels.push(yearStart.getFullYear().toString());
      }
    }

    const baseFilters = {
      referrer_link: {
        referrer: {
          id: referrer[0].id,
        },
      },
    };

    // Get data for each period
    const visitors = [];
    const leads = [];
    const conversions = [];

    for (const { start, end } of periods) {
      const periodFilters = {
        ...baseFilters,
        createdAt: {
          $gte: start.toISOString(),
          $lte: end.toISOString(),
        },
      };

      const [visitorsCount, leadsCount, conversionsCount] = await Promise.all([
        strapi.entityService.count('api::track-link.track-link', {
          filters: { ...periodFilters, type: 'visitors' },
        }),
        strapi.entityService.count('api::track-link.track-link', {
          filters: { ...periodFilters, type: 'leads' },
        }),
        strapi.entityService.count('api::track-link.track-link', {
          filters: { ...periodFilters, type: 'conversions' },
        }),
      ]);

      visitors.push(visitorsCount);
      leads.push(leadsCount);
      conversions.push(conversionsCount);
    }

    return {
      time: timeLabels,
      visitors,
      leads,
      conversions,
    };
  },
}));
