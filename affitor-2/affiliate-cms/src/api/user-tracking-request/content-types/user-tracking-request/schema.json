{"kind": "collectionType", "collectionName": "user_tracking_requests", "info": {"singularName": "user-tracking-request", "pluralName": "user-tracking-requests", "displayName": "User Tracking Request", "description": "Track and limit user requests"}, "options": {"draftAndPublish": true}, "attributes": {"users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "user_tracking_request"}, "request_count": {"type": "integer", "default": 0, "min": 0, "required": true}, "request_limit": {"type": "integer", "default": 200, "min": 1, "required": true}, "last_request_date": {"type": "datetime"}, "statistics": {"type": "json", "description": "Track requests by path"}, "subscription_tier": {"type": "relation", "relation": "manyToOne", "target": "api::subscription-tier.subscription-tier"}, "current_period_end": {"type": "datetime", "description": "End date of current billing period"}, "transaction": {"type": "relation", "relation": "oneToOne", "target": "api::transaction.transaction"}}}