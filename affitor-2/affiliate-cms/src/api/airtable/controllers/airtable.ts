/**
 * airtable controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::airtable.airtable', ({ strapi }) => ({
  async syncAffiliates(ctx) {
    try {
      const response = await strapi.service('api::airtable.airtable').syncAffiliates();

      return {
        data: response,
      };
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  async webhookSync(ctx) {
    try {
      // Validate webhook request (implement proper validation in production)
      const isValid = await strapi.service('api::airtable.airtable').validateWebhook(ctx.request);

      if (!isValid) {
        return ctx.forbidden('Invalid webhook request');
      }

      // Process the webhook sync asynchronously
      strapi.service('api::airtable.airtable').syncAffiliates();

      // Return immediate success response to Airtable
      return {
        message: 'Sync initiated',
      };
    } catch (error) {
      ctx.throw(500, error);
    }
  },
}));
