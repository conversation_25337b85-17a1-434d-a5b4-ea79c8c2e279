import { createHmac } from 'crypto';

export default {
  /**
   * Generate a direct login URL for Discourse
   * This creates a URL that will redirect users to Discourse's login page
   * @returns Direct URL to Discourse login
   */
  getDiscourseLoginUrl: () => {
    const discourseUrl = process.env.DISCOURSE_URL;

    if (!discourseUrl) {
      throw new Error('Discourse URL must be configured');
    }

    // Simply return the Discourse login URL
    // When users click this, Discourse will initiate the SSO flow by redirecting to our SSO endpoint
    return `${discourseUrl}/login`;
  },

  /**
   * Generate Discourse SSO response for authenticated user
   * This method should only be called when we have SSO parameters from Discourse (reverse flow)
   * @param user - The authenticated user object
   * @param sso - The SSO parameter from Discourse (required)
   * @param sig - The signature parameter from Discourse (required)
   * @returns Object containing the SSO URL
   */
  generateSSOUrl: (user: any, sso: string, sig: string) => {
    const discourseUrl = process.env.DISCOURSE_URL;
    const discourseSecret = process.env.DISCOURSE_SSO_SECRET;

    if (!discourseUrl || !discourseSecret) {
      throw new Error('Discourse URL and SSO secret must be configured in environment variables');
    }

    if (!sso || !sig) {
      throw new Error('SSO parameters are required. This method should only be called for reverse SSO flow.');
    }

    // Validate the signature from Discourse
    const expectedSig = createHmac('sha256', discourseSecret).update(sso).digest('hex');
    if (expectedSig !== sig) {
      throw new Error('Invalid SSO signature from Discourse');
    }

    // Decode the SSO payload to extract nonce and return_sso_url
    let nonce: string;
    let returnSsoUrl: string | null = null;

    try {
      const decodedPayload = Buffer.from(sso, 'base64').toString();
      const params = new URLSearchParams(decodedPayload);

      // Extract nonce from Discourse request (required)
      const discourseNonce = params.get('nonce');
      if (!discourseNonce) {
        throw new Error('No nonce found in Discourse SSO payload');
      }
      nonce = discourseNonce;

      // Extract return_sso_url if provided
      const returnUrl = params.get('return_sso_url');
      if (returnUrl) {
        returnSsoUrl = returnUrl;
      }

      strapi.log.info('Discourse SSO request decoded:', { nonce, returnSsoUrl });
    } catch (error) {
      strapi.log.error('Error decoding SSO payload:', error);
      throw new Error('Invalid SSO payload from Discourse');
    }

    // Create the SSO response payload with user data
    const ssoPayload = {
      nonce: nonce, // Use the nonce provided by Discourse
      external_id: user.id.toString(),
      email: user.email,
      username: user.username || user.email.split('@')[0],
      name: user.fullname || user.username || user.email.split('@')[0],
      // Optional fields
      ...(user.avatar && { avatar_url: user.avatar }),
      ...(user.bio && { bio: user.bio }),
      // Set user as admin if they have admin role (optional)
      admin: user.role?.type === 'admin' ? 'true' : 'false',
      // Set user as moderator if they have moderator role (optional)
      moderator: user.role?.type === 'moderator' ? 'true' : 'false',
      // Add return URL if provided
      ...(returnSsoUrl && { return_sso_url: returnSsoUrl }),
    };

    // Convert payload to query string (URL encode values)
    const payloadString = Object.entries(ssoPayload)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');

    strapi.log.info('SSO response payload string:', payloadString);

    // Base64 encode the payload
    const base64Payload = Buffer.from(payloadString).toString('base64');

    // Generate HMAC signature
    const signature = createHmac('sha256', discourseSecret).update(base64Payload).digest('hex');

    // Construct the SSO URL (return to Discourse)
    const ssoUrl = `${discourseUrl}/session/sso_login?sso=${encodeURIComponent(base64Payload)}&sig=${signature}`;

    strapi.log.info('Generated SSO response URL:', { ssoUrl, signature });

    return {
      ssoUrl,
      payload: ssoPayload,
      base64Payload,
      signature,
    };
  },

  /**
   * Validate Discourse SSO return parameters
   * @param sso - Base64 encoded SSO payload
   * @param sig - HMAC signature
   * @returns Decoded payload if valid
   */
  validateSSOReturn: (sso: string, sig: string) => {
    const discourseSecret = process.env.DISCOURSE_SSO_SECRET;

    if (!discourseSecret) {
      throw new Error('Discourse SSO secret must be configured');
    }

    // Verify signature
    const expectedSig = createHmac('sha256', discourseSecret).update(sso).digest('hex');
    if (expectedSig !== sig) {
      throw new Error('Invalid SSO signature');
    }

    // Decode payload
    const decodedPayload = Buffer.from(sso, 'base64').toString();
    const params = new URLSearchParams(decodedPayload);

    return Object.fromEntries(params.entries());
  },

  /**
   * Handle initial SSO request from Discourse
   * This is called when a user clicks "Log In" on Discourse
   * @param sso - Base64 encoded SSO payload from Discourse
   * @param sig - HMAC signature from Discourse
   * @returns Login URL for the frontend with SSO parameters
   */
  handleInitialSSORequest: (sso: string, sig: string) => {
    const discourseSecret = process.env.DISCOURSE_SSO_SECRET;
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

    if (!discourseSecret) {
      throw new Error('Discourse SSO secret must be configured');
    }

    // Verify signature from Discourse
    const expectedSig = createHmac('sha256', discourseSecret).update(sso).digest('hex');
    if (expectedSig !== sig) {
      throw new Error('Invalid SSO signature from Discourse');
    }

    // Decode the payload to extract nonce and return URL
    const decodedPayload = Buffer.from(sso, 'base64').toString();
    const params = new URLSearchParams(decodedPayload);

    // Log the decoded payload for debugging
    strapi.log.info('Initial SSO request from Discourse:', Object.fromEntries(params.entries()));

    // Redirect to frontend login page with SSO parameters
    // The frontend will handle authentication and then call back to our API
    return `${frontendUrl}/authentication?sso=${encodeURIComponent(sso)}&sig=${sig}&discourse=true`;
  },

  /**
   * Handle SSO logout from Discourse
   * @param user - The user to logout
   * @returns Success status
   */
  handleSSOLogout: async (user: any) => {
    // Here you can implement any cleanup logic when user logs out from Discourse
    // For example, you might want to invalidate sessions, log the event, etc.
    
    strapi.log.info(`User ${user.id} logged out from Discourse`);
    
    return {
      success: true,
      message: 'User logged out successfully',
    };
  },

  /**
   * Sync user data with Discourse
   * @param user - The user object to sync
   * @returns Sync status
   */
  // syncUserWithDiscourse: async (user: any) => {
  //   // This method can be used to sync user data changes with Discourse
  //   // You might call this when user profile is updated

  //   try {
  //     const result = discourseSSO.generateSSOUrl(user);
  //     const { ssoUrl } = result;
      
  //     // Log the sync attempt
  //     strapi.log.info(`Syncing user ${user.id} with Discourse`);
      
  //     return {
  //       success: true,
  //       ssoUrl,
  //       message: 'User data prepared for Discourse sync',
  //     };
  //   } catch (error) {
  //     strapi.log.error('Error syncing user with Discourse:', error);
  //     throw error;
  //   }
  // },
};
