I need to implement [FEATURE_DESCRIPTION] in my [TECHNOLOGY_STACK] project. Please follow this comprehensive development workflow:

**Phase 1: Information Gathering & Planning**
1. Use codebase-retrieval to understand the current architecture and identify all relevant files, services, and dependencies
2. Analyze existing patterns, conventions, and similar implementations in the codebase
3. Create a detailed task breakdown using task management tools with meaningful 20-minute work units
4. Identify potential risks, dependencies, and integration points

**Phase 2: Dependency Management**
1. Use appropriate package managers (npm/yarn/pip/cargo/etc.) to install required dependencies
2. Never manually edit package.json, requirements.txt, or similar files - always use package manager commands
3. Verify installations and check for any peer dependency warnings

**Phase 3: Implementation**
1. Create new service files/utilities first using save-file tool (limit to 300 lines)
2. Use str-replace-editor to modify existing files, making targeted changes
3. Always gather detailed context with codebase-retrieval before editing existing code
4. Update imports, method calls, and return types consistently
5. Follow existing code patterns and naming conventions
6. Update task status as work progresses

**Phase 4: Integration & Testing**
1. Verify TypeScript compilation passes (yarn build or tsc)
2. Update any related configuration files
3. Test the implementation with realistic scenarios
4. Write or update unit tests for new functionality
5. Run tests to ensure everything works correctly

**Phase 5: Cleanup & Documentation**
1. Remove deprecated dependencies and unused code
2. Clean up temporary files and imports
3. Update documentation and comments
4. Mark tasks as complete and identify any follow-up work

**Key Requirements:**
- Use task management for complex multi-step work
- Always use package managers for dependency changes
- Gather context before making edits
- Test implementations thoroughly
- Follow existing codebase patterns
- Update related files consistently
- Provide clear progress updates

**Example Request:**
"I need to migrate my file upload system from Google Drive to AWS S3, including updating the TikTok video cover download feature to use S3 storage instead of Google Drive, removing Google Drive dependencies, and ensuring all related services and endpoints work correctly."