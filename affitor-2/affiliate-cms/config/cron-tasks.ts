export default {
  /**
   * Automatically sync affiliates from Airtable on schedule
   * The schedule is configured in the global settings
   */
  airtableSync: {
    task: async ({ strapi }) => {
      try {
        // Log that the cron job is running
        strapi.log.info('Running scheduled Airtable sync job');

        // Call the sync method
        const airtableService = strapi.service('api::airtable.airtable');
        const result = await airtableService.syncAffiliates();

        // Log the result
        strapi.log.info(`Airtable sync completed: ${result.message}`);
      } catch (error) {
        strapi.log.error('Error running scheduled Airtable sync:', error);
      }
    },
    options: {
      // Use a static rule that <PERSON><PERSON><PERSON> can understand at startup
      // This will be overridden when the bootstrap function runs
      rule: '*/5 * * * *', // Run every 5 minutes
    },
  },

  /**
   * Fetch trending ads for affiliates that haven't been processed yet
   */
  fetchAffiliateAds: {
    task: async ({ strapi }) => {
      try {
        strapi.log.info('Running scheduled affiliate ads fetching job');

        // Get global settings to retrieve resolved affiliates and limit
        const globalSettings = await strapi.db.query('api::global.global').findOne();

        // Get the list of resolved affiliates (or initialize empty array if not set)
        const resolvedAffiliates = globalSettings?.resolved_affiliates || [];

        // Get all published affiliates that aren't in the resolved list
        const affiliates = await strapi.documents('api::affiliate.affiliate').findMany({
          filters: {
            id: {
              $notIn: resolvedAffiliates,
            },
          },
          status: 'published',
          populate: ['airtable_data'],
          limit: 5, // Process max 5 affiliates per run to avoid overloading
        });

        strapi.log.info(`Found ${affiliates.length} unprocessed affiliates to fetch ads for`);

        if (affiliates.length === 0) {
          strapi.log.info('No new affiliates to process for ads fetching');
          return;
        }

        // Process each affiliate
        const newlyResolvedAffiliates = [];

        for (const affiliate of affiliates) {
          strapi.log.info(`Processing ads for affiliate ${affiliate.name} (ID: ${affiliate.id})`);

          // Check if we have a URL to process
          const url = affiliate.url || affiliate.airtable_data?.user_ref_link;

          // Skip affiliates with no keywords and no URL
          if (!affiliate.youtube_ad_keyword && !affiliate.tiktok_ad_keyword && !url) {
            strapi.log.info(`Skipping affiliate ${affiliate.name} - no keywords or URL available`);
            continue;
          }

          // Log what we're processing with
          strapi.log.info(
            `Processing affiliate ${affiliate.id} with YouTube keyword: "${affiliate.youtube_ad_keyword || '(none)'}", ` +
              `TikTok keyword: "${affiliate.tiktok_ad_keyword || '(none)'}", URL: ${url || '(none)'}`
          );

          // Simply pass the affiliate object to the service
          // The service will extract the domain keyword, handle platform-specific keywords,
          // and create the log entry
          const searchResult = await strapi
            .service('api::top-ad.top-ad')
            .searchTrendingAds(affiliate, { limit: 20 });

          strapi.log.info(`Completed search for affiliate: ${affiliate.name}`);

          // Add to the list of newly resolved affiliates
          newlyResolvedAffiliates.push(affiliate.id);
        }

        // Update the global settings with newly resolved affiliates
        if (newlyResolvedAffiliates.length > 0) {
          const updatedResolvedAffiliates = [...resolvedAffiliates, ...newlyResolvedAffiliates];

          await strapi.db.query('api::global.global').update({
            where: { id: globalSettings.id },
            data: {
              resolved_affiliates: updatedResolvedAffiliates,
            },
          });

          strapi.log.info(
            `Updated resolved affiliates list, added ${newlyResolvedAffiliates.length} affiliates`
          );
        }

        strapi.log.info('Completed scheduled affiliate ads fetching job');
      } catch (error) {
        strapi.log.error('Error running scheduled affiliate ads fetching job:', error);
      }
    },
    options: {
      rule: '0 0 * * *', // Default to run at midnight every day
    },
  },
};
