#!/bin/bash

# Set variables
ECR_REGISTRY="779037175265.dkr.ecr.us-east-1.amazonaws.com"
ECR_REPOSITORY="affiliate"
IMAGE_TAG="latest"

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REGISTRY

# Build the Docker image
echo "Building Docker image: $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile.prod .

# Push the Docker image to ECR
echo "Pushing Docker image to ECR..."
docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG


echo "Done!"