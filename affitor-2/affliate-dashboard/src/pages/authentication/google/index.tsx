import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { StrapiClient } from "@/utils/request";
import Head from "next/head";
import { useDispatch } from "react-redux";
import { actions } from "@/features/auth/auth.slice";

const GoogleAuthCallback = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  // Fix hydration mismatch by ensuring client-side rendering is consistent
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    // Only run once router is ready and we have query params
    if (!router.isReady) return;

    const { token } = router.query;

    // If no token is provided, show error
    if (!token || typeof token !== "string") {
      setError("No authentication token provided");
      setIsLoading(false);
      return;
    }

    const validateToken = async () => {
      try {
        const response: any = await StrapiClient.validateToken(token);

        if (response?.user) {
          // Store user information in localStorage
        //   localStorage.setItem("auth_token", token);
        //   localStorage.setItem("user", JSON.stringify(response.user));

          dispatch(
            actions.setAuthenticatedUser({
              user: response.user,
              jwt: token,
            })
          );

          // Redirect to dashboard
          router.push("/");
        } else {
          throw new Error("Invalid authentication response");
        }
      } catch (error: any) {
        console.error("Token validation failed:", error);
        setError(error.message || "Authentication failed");
        setIsLoading(false);
      }
    };

    validateToken();
  }, [router.isReady, router.query]);

  // Don't render anything until client-side hydration is complete
  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <Head>
          <title>Authenticating...</title>
        </Head>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3861FB] mx-auto"></div>
          <p className="mt-4 text-gray-600">Completing authentication...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <Head>
          <title>Authentication Error</title>
        </Head>
        <div className="bg-white p-8 rounded-xl shadow-md max-w-md w-full">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            Authentication Error
          </h2>
          <p className="text-gray-700 mb-6">{error}</p>
          <button
            onClick={() => router.push("/authentication")}
            className="w-full bg-[#3861FB] text-white py-2 px-4 rounded-lg hover:bg-[#2D4ECD] transition-colors"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Head>
        <title>Authentication Successful</title>
      </Head>
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3861FB] mx-auto"></div>
        <p className="mt-4 text-gray-600">Redirecting to profile...</p>
      </div>
    </div>
  );
};

export default GoogleAuthCallback;
