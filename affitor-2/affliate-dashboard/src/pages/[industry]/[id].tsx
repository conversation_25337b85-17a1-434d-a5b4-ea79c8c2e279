import { AffiliateDetail } from "@/containers";
import { useRouter } from "next/router";

export default function AffiliateDetailPage() {
  const router = useRouter();
  const { category, id } = router.query;
  
  // You can use the category param if needed
  console.log(`Viewing affiliate ${id} in category: ${category}`);
  
  return (
    <div className="bg-secondary p-5 min-h-screen">
      <div className="max-w-[1400px] mx-auto space-y-4">
        <AffiliateDetail />
      </div>
    </div>
  );
}
