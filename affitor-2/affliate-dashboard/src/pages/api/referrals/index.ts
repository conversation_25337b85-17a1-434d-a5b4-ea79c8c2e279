import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ message: "Authentication required" });
    }

    // Build query string from request parameters
    const queryParams = new URLSearchParams();

    // Handle pagination
    if (req.query.page) {
      queryParams.append("pagination[page]", req.query.page as string);
    }
    if (req.query.pageSize) {
      queryParams.append("pagination[pageSize]", req.query.pageSize as string);
    }

    // Default sorting by creation date (newest first)
    queryParams.append("sort", "createdAt:desc");

    const query = queryParams.toString();
    const response = await StrapiClient.getReferrals(query, token);

    res.status(200).json(response);
  } catch (error: any) {
    console.error("API Error:", error);
    res.status(error.statusCode || 500).json({
      message: error.message || "Internal server error",
    });
  }
}
