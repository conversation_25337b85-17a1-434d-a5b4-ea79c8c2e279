import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ success: boolean; checkoutUrl?: string } | AppError>
) {
  // Only allow POST method
  if (req.method !== "POST") {
    return res.status(405).json({
      message: "Method not allowed",
      statusCode: 405,
    });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    const { tierId } = req.body;

    if (!tierId) {
      return res.status(400).json({
        message: "Subscription tier ID is required",
        statusCode: 400,
      });
    }

    // Forward the request to create a checkout session
    const response = await StrapiClient.createCheckoutSession(tierId, token!);

    // Return the checkout URL
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error creating checkout session:", error);
    sendApiError(res, error, "Error creating checkout session");
  }
}
