import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;
  const { id } = req.query;

  if (method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ 
      statusCode: 400, 
      message: "Page ID is required" 
    });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Call Strapi API to publish page
    const response = await StrapiClient.client.post(`/api/pages/publish/${id}?populate=author`, {}, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    return res.status(200).json(response.data);
  } catch (error: any) {
    console.error("Error publishing page:", error);
    return sendApiError(res, error, "Failed to publish page");
  }
}
