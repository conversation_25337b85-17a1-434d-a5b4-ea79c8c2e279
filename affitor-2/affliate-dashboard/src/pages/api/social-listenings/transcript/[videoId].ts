import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ transcript: string } | AppError>
) {
  try {
    // Use centralized context with required auth
    const { token, cookies } = createApiContext(req, {
      requireAuth: true,
      forwardCookies: true,
    });

    // Extract videoId from query
    const { videoId } = req.query;

    if (!videoId || typeof videoId !== "string") {
      return sendApiError(
        res,
        {
          statusCode: 400,
          message: "Invalid video ID",
        },
        "Invalid video ID"
      );
    }

    // Forward the token and cookies to the backend request
    const response = await StrapiClient.getSocialListeningTranscript(
      videoId,
      token,
      cookies
    );
    console.log("Transcript API response:", response);
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Transcript API error:", error);
    sendApiError(res, error, "Error fetching video transcript");
  }
}
