import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { method } = req;

  if (method !== "PUT") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract payout document IDs from request body
    const { documentIds } = req.body;

    if (
      !documentIds ||
      (!Array.isArray(documentIds) && typeof documentIds !== "string")
    ) {
      return res.status(400).json({ error: "Invalid payout document IDs" });
    }

    // Validate document IDs
    const ids = Array.isArray(documentIds) ? documentIds : [documentIds];
    const validIds = ids.filter(
      (id) => typeof id === "string" && id.trim().length > 0
    );

    if (validIds.length === 0) {
      return res
        .status(400)
        .json({ error: "No valid payout document IDs provided" });
    }

    // Call StrapiAdminClient to mark payouts as paid
    const data = await StrapiAdminClient.markPayoutAsPaid(validIds, token!);

    res.status(200).json(data);
  } catch (error: any) {
    console.error("Mark payouts as paid API error:", error);
    sendApiError(res, error, "Error marking payouts as paid");
  }
}
