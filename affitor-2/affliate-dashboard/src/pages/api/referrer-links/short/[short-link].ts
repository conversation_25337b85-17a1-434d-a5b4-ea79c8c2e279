import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { "short-link": shortCode } = req.query;

    if (!shortCode || typeof shortCode !== "string") {
      return res.status(400).json({
        success: false,
        message: "Short link code is required",
      });
    }

    // Call the StrapiClient method to fetch the short link
    const response = await StrapiClient.getReferrerLinkByShortCode(shortCode);

    // Return the response from Strapi
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching short link:", error);

    // Handle specific error types
    if (error.message?.includes("404") || error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        message: "Short link not found",
      });
    }
	
    // Handle other errors
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
}
