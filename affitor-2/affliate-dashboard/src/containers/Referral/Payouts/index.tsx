import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { payoutActions } from "@/features/rootActions";
import {
  selectPayoutOverview,
  selectPayoutLoading,
  selectPayoutError,
} from "@/features/selectors";
import PayoutOverview from "./Overview";
import PayoutHistory from "./History";

const PayoutsContainer: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  // Redux selectors
  const payoutOverview = useSelector(selectPayoutOverview);
  const isLoading = useSelector(selectPayoutLoading);
  const error = useSelector(selectPayoutError);

  // Fetch payout overview on component mount
  useEffect(() => {
    dispatch(payoutActions.fetchPayoutOverview());
  }, [dispatch]);

  // Show loading state
  if (isLoading && !payoutOverview) {
    return (
      <div className="p-4 sm:p-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="p-4 sm:p-6 max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-5 h-5 text-red-500">❌</div>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-200">
                Error loading payout data: {error}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 max-w-7xl mx-auto space-y-6 sm:space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Payouts
        </h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
          Track your commission payouts and earnings.
        </p>
      </div>

      {/* Overview Section */}
      <PayoutOverview overview={payoutOverview} isLoading={isLoading} />

      {/* History Section */}
      <PayoutHistory />
    </div>
  );
};

export default PayoutsContainer;
