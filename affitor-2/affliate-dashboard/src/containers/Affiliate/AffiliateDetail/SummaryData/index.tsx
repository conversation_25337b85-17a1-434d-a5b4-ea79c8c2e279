import { NumberFormat } from "@/components/NumberFormat";
import PaymentMethod from "@/components/PaymentMethod";
import { IPaymentMethod } from "@/interfaces";
import React from "react";

interface EnhancedSummaryDataProps {
  commission: string;
  monthlyTraffic: number;
  avg_conversion: number;
  currency: string;
  paymentMethods: IPaymentMethod[];
  minimumPayout: number;
  cookieDuration: string;
}

function DataItem({
  title,
  children,
  subtitle,
}: {
  title: string;
  children: React.ReactNode;
  subtitle?: string;
}) {
  return (
    <div className="text-center">
      <h3 className="text-xs font-medium text-gray-400 dark:text-gray-500 uppercase tracking-wider">
        {title}
      </h3>
      <div className="mt-2 text-xl font-bold text-gray-800 dark:text-white">
        {children}
      </div>
      {subtitle && (
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {subtitle}
        </p>
      )}
    </div>
  );
}

export default function EnhancedSummaryData({
  commission,
  monthlyTraffic,
  currency,
  avg_conversion,
  paymentMethods,
  minimumPayout,
  cookieDuration,
}: EnhancedSummaryDataProps) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-y-8 gap-x-4 items-start">
      
      {/* Commission */}
      <DataItem
        title="COMMISSION"
        subtitle="Per Sale"
      >
        {commission || "—"}
      </DataItem>

      {/* EPU (Avg Earning) */}
      <DataItem
        title="EPU"
        subtitle="Per Conversion"
      >
        {avg_conversion ? (
          <>
            {currency === "EUR" ? "€" : "$"}
            <span>{avg_conversion.toFixed(2)}</span>
          </>
        ) : (
          "—"
        )}
      </DataItem>


      {/* Traffic */}
      <DataItem
        title="TRAFFIC"
        subtitle="Monthly"
      >
        <NumberFormat value={monthlyTraffic} />
      </DataItem>

      {/* Payment Methods */}
      <DataItem
        title="PAYMENT"
        subtitle="Methods"
      >
        <div className="flex gap-1.5 flex-wrap justify-center items-center h-full min-h-[28px]">
          {paymentMethods && paymentMethods.length > 0 ? (
            paymentMethods.map((method, i) => (
              <PaymentMethod method={method} key={`payment_${i}`} />
            ))
          ) : (
            "—"
          )}
        </div>
      </DataItem>

      {/* Cookie Duration */}
      <DataItem
        title="COOKIE"
        subtitle="Days"
      >
        {cookieDuration ? cookieDuration : "—"}
      </DataItem>

      {/* Minimum Payout */}
      <DataItem
        title="MIN PAYOUT"
        subtitle="Required"
      >
        {minimumPayout ? (
          <>
            {currency === "EUR" ? "€" : "$"}
            <span>{minimumPayout.toFixed(2)}</span>
          </>
        ) : (
          "—"
        )}
      </DataItem>

    </div>
  );
}