import { IPagination, ITrafficWeb, ISort } from "@/interfaces";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
const selectTrafficWebState = (state: RootState) => state.trafficWeb;

interface TrafficWebState {
  list: ITrafficWeb[] | null;
  loading: boolean;
  pagination?: IPagination;
  current?: ITrafficWeb | null;
  error?: string | null;
}

const initialState: TrafficWebState = {
  list: null,
  loading: false,
  current: null,
  error: null,
};

const trafficWebSlice = createSlice({
  name: "trafficWeb",
  initialState,
  reducers: {
    // Sync actions
    setTrafficWebs: (state, action: PayloadAction<ITrafficWeb[] | null>) => {
      state.list = action.payload;
    },

    fetch: (
      state,
      action: PayloadAction<{
        affiliateDocId: string;
        pagination?: IPagination;
        sort?: ISort;
      }>
    ) => {},

    setTrafficWeb: (state, action: PayloadAction<ITrafficWeb>) => {
      state.current = action.payload;
    },

    setPagination: (state, action: PayloadAction<IPagination>) => {
      state.pagination = action.payload;
    },

    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },

    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
  },
});

export const { actions, reducer } = trafficWebSlice;

// social listening
export const selectTrafficWebList = createSelector(
  [selectTrafficWebState],
  (trafficWebState) => trafficWebState.list
);

export const selectTrafficWebLoading = createSelector(
  [selectTrafficWebState],
  (trafficWebState) => trafficWebState.loading
);

export const selectTrafficWebCurrent = createSelector(
  [selectTrafficWebState],
  (trafficWebState) => trafficWebState.current
);

export const selectTrafficWebPagination = createSelector(
  [selectTrafficWebState],
  (trafficWebState) => trafficWebState.pagination
);

export const selectErrorTrafficWeb = createSelector(
  [selectTrafficWebState],
  (trafficWebState) => trafficWebState.error
);
