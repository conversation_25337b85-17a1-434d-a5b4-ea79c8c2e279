import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./referral-activity.slice";
import { handleApiError } from "@/utils/error-handler";
import { PayloadAction } from "@reduxjs/toolkit";
import qs from "qs";
import { IPagination, ISort } from "@/interfaces";

function* fetchActivitiesSaga(
  action: PayloadAction<{
    pagination?: IPagination;
    sort?: ISort;
  }>
): Generator<any, void, any> {
  try {
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
    const { pagination, sort } = action.payload;

    const query = qs.stringify(
      {
        ...(pagination && {
          pagination: {
            page: pagination.page,
            pageSize: pagination.pageSize || 25,
          },
        }),
        ...(sort && {
          sort: [sort].flat().map((s) => `${s.field}:${s.order}`),
        }),
      },
      {
        encodeValuesOnly: true,
      }
    );

    const headers: any = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response: any = yield call(fetch, `/api/referral-activity?${query}`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      yield put(
        actions.fetchActivitiesFailure(
          `Request failed with status ${response.status}`
        )
      );
      return;
    }

    const result = yield response.json();

    if (!result.data || !result.meta) {
      yield put(actions.fetchActivitiesFailure("Invalid response structure"));
      return;
    }

    yield put(actions.fetchActivitiesSuccess(result));
  } catch (error: any) {
    yield put(
      actions.fetchActivitiesFailure(
        error.message || "Failed to fetch referral activities"
      )
    );
  }
}

export default function* referralActivitySaga() {
  yield takeEvery(actions.fetchActivitiesRequest.type, fetchActivitiesSaga);
}
