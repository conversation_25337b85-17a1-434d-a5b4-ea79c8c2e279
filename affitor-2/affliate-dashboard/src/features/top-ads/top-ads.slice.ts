import { IPagination, IAd, ISort } from "@/interfaces";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { VideoFilterState } from "@/containers/TopVideos/FilterPanel";

const selectTopAdsState = (state: RootState) => state.topAds;

interface TopAdsState {
  list: IAd[] | null;
  loading: boolean;
  pagination: IPagination;
  error: string | null;
  sort: { field: string; order: "desc" | "asc" };
  timePeriod: string;
  filters: VideoFilterState;
  searchTerm: string;
  transcript: string | null;
  loadingTranscript: boolean;
  loadingTranscriptId: string | null;
  currentAd: IAd | null;
}

const initialState: TopAdsState = {
  list: null,
  loading: false,
  pagination: {
    page: 1,
    pageSize: 10,
    pageCount: 0,
    total: 0,
  },
  error: null,
  sort: { field: "views", order: "desc" },
  timePeriod: "30days",
  filters: {
    platforms: [],
    categories: [],
    minViews: "",
  },
  searchTerm: "",
  transcript: null,
  loadingTranscript: false,
  loadingTranscriptId: null,
  currentAd: null,
};

const topAdsSlice = createSlice({
  name: "top-ads",
  initialState,
  reducers: {
    fetchTopAds(
      state,
      action: PayloadAction<{
        platforms: string[];
        pagination: IPagination;
        sort: ISort[];
        dateFilter?: any;
        searchTerm?: string;
      }>
    ) {
      state.loading = true;
    },
    setTopAds(state, action: PayloadAction<IAd[] | null>) {
      state.loading = false;
      if (state.list && state.list.length && action.payload) {
        const ids = new Set(state.list.map((item) => item.documentId));
        state.list = [
          ...state.list,
          ...action.payload.filter((item) => !ids.has(item.documentId)),
        ];
      } else {
        state.list = action.payload;
      }
    },
    setPagination(state, action: PayloadAction<IPagination>) {
      state.pagination = action.payload;
    },
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
      state.loading = false;
    },
    setSortOption(
      state,
      action: PayloadAction<{ field: string; order: "desc" | "asc" }>
    ) {
      state.sort = action.payload;
    },
    setTimePeriod(state, action: PayloadAction<string>) {
      state.timePeriod = action.payload;
    },
    setFilters(state, action: PayloadAction<VideoFilterState>) {
      state.filters = action.payload;
    },
    setSearchTerm(state, action: PayloadAction<string>) {
      state.searchTerm = action.payload;
    },
    // New actions for transcript handling
    fetchAdTranscript(state, action: PayloadAction<string>) {
      state.loadingTranscript = true;
      state.loadingTranscriptId = action.payload;
    },
    setAdTranscript(state, action: PayloadAction<string | null>) {
      state.transcript = action.payload;
      state.loadingTranscript = false;
      state.loadingTranscriptId = null;
    },
    setLoadingTranscript(state, action: PayloadAction<boolean>) {
      state.loadingTranscript = action.payload;
      if (!action.payload) {
        state.loadingTranscriptId = null;
      }
    },
    setCurrentAd(state, action: PayloadAction<IAd | null>) {
      state.currentAd = action.payload;
    },
  },
});

export const reducer = topAdsSlice.reducer;
export const actions = topAdsSlice.actions;

export const selectTopAdsList = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.list
);

export const selectTopAdsLoading = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.loading
);

export const selectTopAdsPagination = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.pagination
);

export const selectTopAdsError = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.error
);

// New selectors for transcript functionality
export const selectAdTranscript = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.transcript
);

export const selectLoadingAdTranscript = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.loadingTranscript
);

export const selectLoadingAdTranscriptId = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.loadingTranscriptId
);

export const selectCurrentAd = createSelector(
  [selectTopAdsState],
  (topAdsState) => topAdsState.currentAd
);
