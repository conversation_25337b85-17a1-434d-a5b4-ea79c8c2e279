import { DateRange, DateRangePicker } from "mui-daterange-picker";
import { useState } from "react";
import { CalendarSVG } from "./Icons";

export function DateTimePicker({ title }: { title: string }) {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [open, setOpen] = useState(false);

  function handleChange(range: DateRange) {
    setStartDate(range.startDate ?? null);
    setEndDate(range.endDate ?? null);
    console.log(range.startDate, range.endDate);
  }

  return (
    <div className="w-fit">
      <div
        onClick={() => setOpen(!open)}
        className="px-2 py-1 rounded-lg cursor-pointer bg-secondary flex gap-2 items-center"
      >
        <CalendarSVG className="" />
        {startDate && endDate ? (
          <>
            {`${startDate.toLocaleString("default", {
              month: "short",
            })} ${startDate.getFullYear()} ~ ${endDate.toLocaleString(
              "default",
              { month: "long" }
            )} ${endDate.getFullYear()}`}
          </>
        ) : (
          title
        )}
      </div>
      <div className="absolute right-0">
        <DateRangePicker
          onChange={handleChange}
          open={open}
          toggle={() => setOpen(!open)}
        />
      </div>
    </div>
  );
}
