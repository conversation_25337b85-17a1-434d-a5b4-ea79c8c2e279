"use client";

import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/router";
import { useTheme } from "next-themes";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  User,
  Mail,
  Settings,
  Users,
  Rocket,
  Moon,
  Sun,
  LogOut,
  Calendar,
  Tag,
} from "lucide-react";
import { actions as authActions } from "@/features/auth/auth.slice";
import { actions as userActions } from "@/features/user/user.slice";
import {
  selectUserData,
  selectUserLoading,
  selectUserError,
} from "@/features/selectors";

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode; // The Profile button that triggers the modal
}

export const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose,
  children,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const userData = useSelector(selectUserData);
  const loading = useSelector(selectUserLoading);
  const error = useSelector(selectUserError);

  // Use the same data binding approach as the Profile container
  React.useEffect(() => {
    if (isOpen && !userData) {
      dispatch(userActions.fetchUserMe());
    }
  }, [dispatch, isOpen, userData]);

  // Use the same data calculation approach as the Profile container
  const subscription_tier = userData?.user_tracking_request?.subscription_tier;
  const transaction = userData?.user_tracking_request?.transaction;
  const request_limit = userData?.user_tracking_request?.request_limit || 0;

  // Check if subscription is cancelled but still active (grace period)
  const isCancelled =
    transaction?.cancellation_date !== null &&
    transaction?.cancellation_date !== undefined;
  const willAutoRenew = transaction?.auto_renew === true;

  // Check if user has unlimited requests (request_limit > 10000)
  const hasUnlimitedRequests = request_limit > 10000;

  const usageData = userData?.user_tracking_request
    ? {
        usageCount: userData.user_tracking_request.request_count,
        maxRequests: userData.user_tracking_request.request_limit,
        last_request_date: new Date(
          userData.user_tracking_request.last_request_date
        ),
        // Calculate days left using current_period_end
        daysLeft: userData.user_tracking_request.current_period_end
          ? Math.max(
              0,
              Math.floor(
                (new Date(
                  userData.user_tracking_request.current_period_end
                ).getTime() -
                  new Date().getTime()) /
                  (1000 * 60 * 60 * 24)
              )
            )
          : 30,
        tier: subscription_tier,
        isCancelled: isCancelled,
        willAutoRenew: willAutoRenew,
        cancellationDate: transaction?.cancellation_date
          ? new Date(transaction.cancellation_date)
          : null,
        cancellationReason: transaction?.cancellation_reason,
        currentPeriodEnd: transaction?.current_period_end
          ? new Date(transaction.current_period_end)
          : null,
        hasUnlimitedRequests: hasUnlimitedRequests,
      }
    : {
        usageCount: 0,
        maxRequests: 0,
        daysLeft: 30,
        tier: subscription_tier,
        isCancelled: false,
        willAutoRenew: false,
        cancellationDate: null,
        cancellationReason: null,
        currentPeriodEnd: null,
        hasUnlimitedRequests: false,
      };

  const usagePercentage =
    usageData.maxRequests > 0
      ? (usageData.usageCount / usageData.maxRequests) * 100
      : 0;

  const formatDateYMD = (date?: string | Date): string => {
    if (!date) return "N/A";
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toISOString().split('T')[0]; // Returns yyyy-mm-dd format
  };

  // Determine if user has a paid subscription
  const hasPaidSubscription = !!subscription_tier;

  // Determine if user should see upgrade button (Free or Pro users)
  const shouldShowUpgradeButton = () => {
    if (!subscription_tier) {
      // No subscription tier means Free user
      return true;
    }

    const tierName = subscription_tier.display_name?.toLowerCase() || '';
    // Show upgrade button for Free, Basic, and Pro users, hide for Premium users
    return (
      tierName === 'free' ||
      tierName === 'basic' ||
      tierName === 'pro' ||
      tierName === 'pro plan' ||
      tierName.includes('free') ||
      tierName.includes('basic') ||
      (tierName.includes('pro') && !tierName.includes('premium'))
    );
  };

  const handleSignOut = () => {
    dispatch(authActions.logout());
    dispatch(userActions.clearUserData());
    onClose();
    router.push("/");
  };

  const handleUserSettings = () => {
    onClose();
    router.push("/profile");
  };

  const handleBecomeAffiliate = () => {
    onClose();
    router.push("/affiliate");
  };

  const handleUpgradePlan = () => {
    onClose();
    router.push("/pricing");
  };

  // Close modal when clicking outside or pressing escape
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  if (loading) {
    return (
      <Popover open={isOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-4"
          align="end"
          side="bottom"
          sideOffset={8}
        >
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  if (error) {
    return (
      <Popover open={isOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-4"
          align="end"
          side="bottom"
          sideOffset={8}
        >
          <div className="text-center py-4">
            <p className="text-red-600 dark:text-red-400 text-sm">
              Failed to load profile data
            </p>
            <Button
              onClick={() => dispatch(userActions.fetchUserMe())}
              variant="outline"
              size="sm"
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent
        className="w-80 p-0 gap-0"
        align="end"
        side="bottom"
        sideOffset={8}
      >
        {/* <div className="p-4 pb-3 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors cursor-pointer"
               onClick={handleBackToDashboard}>
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </div>
        </div> */}

        <div className="p-4 space-y-4">
          {/* Personal Information */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-blue-500" />
              <h2 className="text-lg font-semibold dark:text-white">
                {userData?.username || "Anonymous User"}
              </h2>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Mail className="w-4 h-4" />
              <span>{userData?.email || "N/A"}</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Calendar className="w-4 h-4" />
              <span>Joined: {formatDateYMD(userData?.createdAt)}</span>
            </div>
          </div>

          {/* Usage Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium dark:text-white">Usage Statistics</h3>
              <div className="flex items-center gap-1">
                <Tag className="w-3 h-3 text-blue-500" />
                <span className="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                  {subscription_tier?.display_name || "Free"}
                  {usageData.isCancelled && (
                    <span className="ml-1 text-red-500">(Cancelled)</span>
                  )}
                </span>
              </div>
            </div>

            {/* Monthly Usage */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="font-medium dark:text-white">Monthly Credits</span>
                <span className="text-gray-600 dark:text-gray-400">
                  {usageData.hasUnlimitedRequests ? (
                    "Unlimited"
                  ) : (
                    <>
                      {usageData.usageCount}/{usageData.maxRequests}
                    </>
                  )}
                </span>
              </div>
              <Progress
                value={usageData.hasUnlimitedRequests ? 100 : usagePercentage}
                className="h-2"
              />
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                {usageData.hasUnlimitedRequests ? (
                  <span>Unlimited usage available</span>
                ) : (
                  <>
                    <span>{usagePercentage.toFixed(1)}% used</span>
                    <span>{usageData.maxRequests - usageData.usageCount} left</span>
                  </>
                )}
              </div>
            </div>

            {/* Renewal Info */}
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {hasPaidSubscription
                ? usageData.isCancelled
                  ? `Access until ${formatDateYMD(
                      usageData.currentPeriodEnd?.toISOString()
                    )}`
                  : `Renews on ${formatDateYMD(
                      usageData.currentPeriodEnd?.toISOString()
                    )}`
                : `Resets on ${formatDateYMD(
                    new Date(
                      new Date().getFullYear(),
                      new Date().getMonth() + 1,
                      1
                    ).toISOString()
                  )}`}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2 border-t border-gray-100 dark:border-gray-700 pt-3">
            <Button
              onClick={handleUserSettings}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm"
            >
              <Settings className="w-4 h-4" />
              User Settings
            </Button>

            <Button
              onClick={handleBecomeAffiliate}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm"
            >
              <Users className="w-4 h-4" />
              Become an Affiliate
            </Button>

            {/* Upgrade Plan - Show for Free and Pro users, hide for Premium users */}
            {shouldShowUpgradeButton() && (
              <Button
                onClick={handleUpgradePlan}
                variant="ghost"
                className="w-full justify-start gap-3 h-10 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 dark:text-orange-400 dark:hover:text-orange-300 dark:hover:bg-orange-900/20"
              >
                <Rocket className="w-4 h-4" />
                Upgrade Plan
              </Button>
            )}

            <Button
              onClick={toggleTheme}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm"
            >
              {theme === "light" ? (
                <Moon className="w-4 h-4" />
              ) : (
                <Sun className="w-4 h-4" />
              )}
              {theme === "light" ? "Dark Mode" : "Light Mode"}
            </Button>

            <Button
              onClick={handleSignOut}
              variant="ghost"
              className="w-full justify-start gap-3 h-10 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ProfileModal;
